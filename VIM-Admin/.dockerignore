# ================================
# VIM前端服务 Docker忽略文件
# ================================

# 依赖目录
node_modules/

# 构建输出
dist/
!dist/

# 开发文件
src/
public/
index.html
vite.config.js

# 包管理文件
package.json
package-lock.json
pnpm-lock.yaml

# IDE文件
.idea/
.vscode/
*.iml

# 日志文件
logs/
*.log

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# Git文件
.git/
.gitignore

# 文档文件
*.md
docs/
doc/

# 环境文件
.env
.env.local
.env.development
.env.production

# 缓存文件
.cache/
.parcel-cache/

# 测试文件
tests/
test/
__tests__/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar
