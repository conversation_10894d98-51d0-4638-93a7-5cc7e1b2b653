/**
 * 仪表盘图表处理组合式函数
 * 提供图表初始化、更新和资源管理的通用方法
 */
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

/**
 * 仪表盘图表处理组合式函数
 * @param {Object} options - 配置选项
 * @param {import('vue').Ref<string>} options.timeRange - 时间范围
 * @returns {Object} 图表相关的状态和方法
 */
export function useDashboardCharts({ timeRange }) {
  // 图表引用
  const mainChartRef = ref(null)
  const pieChartRef = ref(null)
  let mainChart = null
  let pieChart = null

  /**
   * 初始化趋势图表
   * @param {Object} data - 图表数据
   */
  const initMainChart = (data) => {
    if (mainChart) {
      mainChart.dispose()
    }

    if (!mainChartRef.value) return

    mainChart = echarts.init(mainChartRef.value)

    // 修复：直接使用后端返回的数据结构
    let timeLabels = data.timeLabels || []
    const openingCounts = data.openingCounts || []
    const salesAmounts = data.salesAmounts || []

    // 处理timeLabels中的null值
    timeLabels = timeLabels.map(label => label === null ? '' : label)

    // 如果timeLabels为空或全部为空字符串，则根据当前时间范围生成默认标签
    if (!timeLabels.length || timeLabels.every(label => label === '')) {
      timeLabels = generateDefaultTimeLabels(timeRange.value)
    }
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        }
      },
      legend: {
        data: ['开箱次数', '销售额(元)'],
        right: 10,
        top: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: timeLabels,
          axisLabel: {
            rotate: timeRange.value === 'month' ? 45 : 0,
            formatter: function(value) {
              return value === null || value === undefined ? '' : value
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '开箱次数',
          position: 'left'
        },
        {
          type: 'value',
          name: '销售额(元)',
          position: 'right',
          axisLabel: {
            formatter: '{value} 元'
          }
        }
      ],
      series: [
        {
          name: '开箱次数',
          type: 'line',
          data: openingCounts,
          itemStyle: {
            color: '#409EFF'
          },
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ]
            }
          }
        },
        {
          name: '销售额(元)',
          type: 'line',
          yAxisIndex: 1,
          data: salesAmounts,
          itemStyle: {
            color: '#F56C6C'
          },
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3
          }
        }
      ]
    }
    
    mainChart.setOption(option)
  }

  /**
   * 生成默认的时间标签
   * @param {string} timeType - 时间类型 ('day', 'week', 'month')
   * @returns {Array<string>} 时间标签数组
   */
  const generateDefaultTimeLabels = (timeType) => {
    const now = new Date()
    const labels = []
    
    if (timeType === 'day') {
      // 生成今日24小时的标签
      for (let i = 0; i < 24; i++) {
        labels.push(`${i}:00`)
      }
    } else if (timeType === 'week') {
      // 生成本周7天的标签
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const today = now.getDay()
      for (let i = 0; i < 7; i++) {
        const day = (today - 6 + i + 7) % 7
        labels.push(weekdays[day])
      }
    } else if (timeType === 'month') {
      // 生成本月天数的标签
      const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
      for (let i = 1; i <= daysInMonth; i++) {
        labels.push(`${i}日`)
      }
    }
    
    return labels
  }

  /**
   * 初始化饼图
   * @param {Object} data - 图表数据
   */
  const initPieChart = (data) => {
    if (pieChart) {
      pieChart.dispose()
    }
    
    if (!pieChartRef.value) return
    
    pieChart = echarts.init(pieChartRef.value)
    
    // 准备饼图数据
    const pieData = []
    const colors = {
      1: '#E6A23C',  // 保密级
      2: '#F56C6C',  // 传说级
      3: '#67C23A',  // 隐秘级
      4: '#409EFF'   // 受限级
    }
    
    // 修复：直接使用后端返回的数据结构
    const itemLevels = data.itemLevels || []
    const counts = data.counts || []
    const percentages = data.percentages || []
    
    // 创建图例数据
    const legendData = []
    
    for (let i = 0; i < itemLevels.length; i++) {
      const levelName = getRarityName(itemLevels[i])
      legendData.push(levelName)
      
      pieData.push({
        name: levelName,
        value: counts[i],
        percentage: percentages[i] || 0, // 使用API返回的百分比数据
        itemStyle: {
          color: colors[itemLevels[i]] || '#909399'
        }
      })
    }
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          // 使用API返回的百分比数据
          return `${params.seriesName} <br/>${params.name}: ${params.value} (${params.data.percentage.toFixed(2)}%)`
        }
      },
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        data: legendData
      },
      series: [
        {
          name: '开箱结果',
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['50%', '38%'],
          data: pieData,
          animationEasing: 'cubicInOut',
          animationDuration: 1000
        }
      ]
    }
    
    pieChart.setOption(option)
  }

  /**
   * 根据等级数字获取等级名称
   * @param {number} level - 等级数字
   * @returns {string} 等级名称
   */
  const getRarityName = (level) => {
    switch (level) {
      case 1: return '传说级'
      case 2: return '保密级'
      case 3: return '隐秘级'
      case 4: return '受限级'
      default: return '普通级'
    }
  }

  /**
   * 窗口大小调整处理
   */
  const handleResize = () => {
    mainChart && mainChart.resize()
    pieChart && pieChart.resize()
  }

  // 监听时间范围变化
  watch(timeRange, () => {
    // 仅在组件挂载后监听，避免初始化时重复调用
    if (mainChart) {
      // 这里不直接调用 fetchBoxTrend，而是返回一个状态供外部使用
      return true
    }
    return false
  })

  // 组件挂载时添加窗口大小调整监听
  onMounted(() => {
    window.addEventListener('resize', handleResize)
  })

  // 组件卸载时移除监听并释放图表资源
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    
    if (mainChart) {
      mainChart.dispose()
      mainChart = null
    }
    
    if (pieChart) {
      pieChart.dispose()
      pieChart = null
    }
  })

  return {
    mainChartRef,
    pieChartRef,
    initMainChart,
    initPieChart,
    getRarityName
  }
} 