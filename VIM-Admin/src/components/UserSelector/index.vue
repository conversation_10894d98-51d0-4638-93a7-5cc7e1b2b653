<template>
  <div class="user-selector">
    <el-select
        v-model="selectedUserId"
        filterable
        remote
        reserve-keyword
        placeholder="请输入用户昵称或手机号进行搜索"
        :remote-method="searchUsers"
        :loading="loading"
        clearable
        style="width: 100%"
        @change="handleUserChange"
        @clear="handleClear"
    >
      <el-option
          v-for="user in userOptions"
          :key="user.id"
          :label="`${user.nickname} (${user.phone}) - ID: ${user.id}`"
          :value="user.id"
      >
        <span style="float: left">{{ user.nickname }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{ user.phone }}</span>
      </el-option>
    </el-select>
    
    <!-- 选中用户信息显示 -->
    <div v-if="selectedUser" class="selected-user-info">
      <el-tag type="success" size="small">
        <i class="el-icon-user"></i>
        {{ selectedUser.nickname }} ({{ selectedUser.phone }}) - ID: {{ selectedUser.id }}
      </el-tag>
    </div>
    
    <!-- 帮助文本 -->
    <div class="help-text">
      <small style="color: #909399;">
        {{ helpText || '输入用户昵称或手机号进行搜索，支持模糊匹配' }}
      </small>
    </div>
  </div>
</template>

<script setup name="UserSelector">
import { ref, watch, onMounted } from 'vue'
import { searchUsers as searchUsersApi } from '@/api/vimRoll/condition'

// Props
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  helpText: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入用户昵称或手机号进行搜索'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'userSelected'])

// 响应式数据
const selectedUserId = ref(props.modelValue)
const selectedUser = ref(null)
const userOptions = ref([])
const loading = ref(false)

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  selectedUserId.value = newValue
  if (newValue) {
    loadUserInfo(newValue)
  } else {
    selectedUser.value = null
  }
}, { immediate: true })

// 监听selectedUserId变化
watch(selectedUserId, (newValue) => {
  emit('update:modelValue', newValue)
})

// 搜索用户
const searchUsers = (keyword) => {
  if (!keyword) {
    userOptions.value = []
    return
  }

  loading.value = true
  searchUsersApi({
    keyword: keyword,
    page: 1,
    pageSize: 20
  }).then(response => {
    userOptions.value = response.rows || []
    loading.value = false
  }).catch(error => {
    console.error('搜索用户失败:', error)
    userOptions.value = []
    loading.value = false
  })
}

// 处理用户选择变化
const handleUserChange = (userId) => {
  if (userId) {
    const user = userOptions.value.find(u => u.id === userId)
    if (user) {
      selectedUser.value = user
      emit('change', userId, user)
      emit('userSelected', user)
    }
  } else {
    selectedUser.value = null
    emit('change', null, null)
    emit('userSelected', null)
  }
}

// 处理清除
const handleClear = () => {
  selectedUser.value = null
  userOptions.value = []
  emit('change', null, null)
  emit('userSelected', null)
}

// 加载用户信息（用于初始化时显示已选择的用户）
const loadUserInfo = async (userId) => {
  if (!userId) return
  
  try {
    loading.value = true
    const response = await searchUsersApi({
      keyword: userId.toString(),
      page: 1,
      pageSize: 1
    })
    
    if (response.rows && response.rows.length > 0) {
      // 查找精确匹配的用户
      const user = response.rows.find(u => u.id === parseInt(userId))
      if (user) {
        selectedUser.value = user
        userOptions.value = [user] // 将用户添加到选项中
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载用户信息
onMounted(() => {
  if (props.modelValue) {
    loadUserInfo(props.modelValue)
  }
})
</script>

<style scoped>
.user-selector {
  width: 100%;
}

.selected-user-info {
  margin-top: 8px;
}

.help-text {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.el-select {
  width: 100%;
}

.el-tag {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
