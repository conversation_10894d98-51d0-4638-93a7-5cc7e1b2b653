<template>
  <span>{{ displayText }}</span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  dict: {
    type: Array,
    required: true,
    default: () => []
  },
  modelValue: {
    type: [String, Number, Boolean],
    default: null
  },
  placeholder: {
    type: String,  // 确保默认值类型为字符串
    default: '--'
  },
  strict: {
    type: Boolean,
    default: false
  }
})

const displayText = computed(() => {
  // 处理空值
  if ([null, undefined, ''].includes(props.modelValue)) {
    return props.placeholder
  }
  // 匹配逻辑
  const foundItem = props.dict.find(item => {
    return props.strict ? item.value === props.modelValue : item.value == props.modelValue  // 使用宽松匹配
  })

  // 返回结果或占位符（确保始终返回字符串）
  return foundItem?.label?.toString() || props.placeholder
})
</script>