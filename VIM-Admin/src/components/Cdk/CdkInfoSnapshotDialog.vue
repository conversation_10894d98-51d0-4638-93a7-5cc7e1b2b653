<template>
  <el-dialog title="CDK信息快照" v-model="visible" width="800px" append-to-body>
    <div v-if="snapshotData">
      <!-- CDK基本信息 -->
      <el-card class="snapshot-card" shadow="never">
        <template #header>
          <span class="card-title">CDK信息</span>
        </template>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="CDK类型">{{ snapshotData.cdkInfoType }}</el-descriptions-item>
          <el-descriptions-item label="快照时间">{{ formatSnapshotTime(snapshotData.snapshotTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 用户基本信息 -->
      <el-card class="snapshot-card" shadow="never">
        <template #header>
          <span class="card-title">用户基本信息</span>
        </template>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="用户ID">{{ snapshotData.userBasicInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="用户昵称">{{ snapshotData.userBasicInfo.nickname }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ snapshotData.userBasicInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ snapshotData.userBasicInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="用户等级">{{ snapshotData.userBasicInfo.level }}</el-descriptions-item>
          <el-descriptions-item label="用户经验">{{ snapshotData.userBasicInfo.exp }}</el-descriptions-item>
          <el-descriptions-item label="用户身份">{{ snapshotData.userBasicInfo.identityName }}</el-descriptions-item>
          <el-descriptions-item label="用户状态">{{ snapshotData.userBasicInfo.stateName }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ snapshotData.userBasicInfo.createTime }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ snapshotData.userBasicInfo.lastLoginTime || '未登录' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 用户资产信息 -->
      <el-card class="snapshot-card" shadow="never">
        <template #header>
          <span class="card-title">用户资产信息</span>
        </template>
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="账户余额">{{ formatDecimal(snapshotData.userAssetInfo.balance) }}</el-descriptions-item>
          <el-descriptions-item label="钥匙数量">{{ formatDecimal(snapshotData.userAssetInfo.keyAmount) }}</el-descriptions-item>
          <el-descriptions-item label="背包价值">{{ formatDecimal(snapshotData.userAssetInfo.backpackValue) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 上级用户信息 -->
      <el-card class="snapshot-card" shadow="never">
        <template #header>
          <span class="card-title">上级用户信息</span>
        </template>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="上级用户ID">{{ snapshotData.superiorUserInfo.inviteUser || '无' }}</el-descriptions-item>
          <el-descriptions-item label="上级昵称">{{ snapshotData.superiorUserInfo.superiorNickname || '无上级' }}</el-descriptions-item>
          <el-descriptions-item label="上级手机号">{{ snapshotData.superiorUserInfo.superiorPhone || '无' }}</el-descriptions-item>
          <el-descriptions-item label="上级身份">{{ snapshotData.superiorUserInfo.superiorIdentityName || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 如果不是JSON格式，显示原始信息 -->
    <div v-else-if="rawInfo">
      <el-card class="snapshot-card" shadow="never">
        <template #header>
          <span class="card-title">CDK信息</span>
        </template>
        <p>{{ rawInfo }}</p>
      </el-card>
    </div>

    <!-- 无信息 -->
    <div v-else>
      <el-empty description="暂无CDK信息" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  cdkInfo: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(props.modelValue)

// 计算属性：解析CDK信息
const snapshotData = computed(() => {
  if (!props.cdkInfo) return null
  
  try {
    const parsed = JSON.parse(props.cdkInfo)
    // 检查是否是快照格式
    if (parsed.userBasicInfo && parsed.userAssetInfo && parsed.superiorUserInfo) {
      return parsed
    }
    return null
  } catch (e) {
    return null
  }
})

// 计算属性：原始信息
const rawInfo = computed(() => {
  if (snapshotData.value) return null
  return props.cdkInfo
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 格式化快照时间
const formatSnapshotTime = (timeStr) => {
  if (!timeStr) return '未知'
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN')
  } catch (e) {
    return timeStr
  }
}

// 格式化小数（保留两位小数）
const formatDecimal = (value) => {
  if (value === null || value === undefined) return '0.00'
  return Number(value).toFixed(2)
}
</script>

<style scoped>
.snapshot-card {
  margin-bottom: 16px;
}

.snapshot-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-weight: bold;
  color: #303133;
}

.el-descriptions {
  background-color: #fafafa;
}
</style>
