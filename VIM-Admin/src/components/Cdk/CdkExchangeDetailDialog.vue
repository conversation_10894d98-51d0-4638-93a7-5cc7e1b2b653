<template>
  <el-dialog title="CDK兑换详情" v-model="visible" width="800px" append-to-body>
    <div v-if="cdkInfo.cdk">
      <el-descriptions :column="2" border style="margin-bottom: 20px;">
        <el-descriptions-item label="CDK码">{{ cdkInfo.cdk }}</el-descriptions-item>
        <el-descriptions-item label="CDK类型">{{ cdkInfo.typeName }}</el-descriptions-item>
        <el-descriptions-item label="CDK数值">{{ cdkInfo.value }}</el-descriptions-item>
        <el-descriptions-item label="CDK状态">{{ cdkInfo.stateName }}</el-descriptions-item>
      </el-descriptions>

      <!-- 兑换详情列表 -->
      <el-table v-loading="loading" :data="exchangeDetailList">
        <el-table-column label="用户ID" align="center" prop="userId" width="80"/>
        <el-table-column label="用户昵称" align="center" prop="nickname" width="120"/>
        <el-table-column label="用户手机号" align="center" prop="phone" width="130"/>
        <el-table-column label="兑换时间" align="center" prop="exchangeTime" width="160"/>
        <el-table-column label="兑换来源" align="center" prop="sourceName" width="120"/>
      </el-table>

      <!-- 分页 -->
      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.pageSize"
          @pagination="getExchangeDetailList"
          style="margin-top: 20px;"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { getCdkExchangeDetail } from '@/api/cdk'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  cdkInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(props.modelValue)
const loading = ref(false)
const exchangeDetailList = ref([])
const total = ref(0)

// 查询参数
const queryParams = ref({
  cdk: '',
  page: 1,
  pageSize: 10
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.cdkInfo.cdk) {
    queryParams.value.cdk = props.cdkInfo.cdk
    getExchangeDetailList()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取兑换详情列表
const getExchangeDetailList = () => {
  if (!queryParams.value.cdk) return
  
  loading.value = true
  getCdkExchangeDetail(queryParams.value).then(response => {
    exchangeDetailList.value = response.rows || []
    total.value = response.total || 0
  }).catch(() => {
    exchangeDetailList.value = []
    total.value = 0
  }).finally(() => {
    loading.value = false
  })
}
</script>
