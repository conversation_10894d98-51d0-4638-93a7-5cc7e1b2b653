<template>
  <el-button
      :type="type"
      :plain="plain"
      :icon="icon"
      :loading="loading"
      :disabled="disabled"
      @click="handleExport"
      v-hasPermi="permissions"
  >
    {{ text }}
  </el-button>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { Download } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  text: {
    type: String,
    default: '导出'
  },
  type: {
    type: String,
    default: 'warning'
  },
  plain: {
    type: Boolean,
    default: true
  },
  icon: {
    type: [String, Object],
    default: () => Download
  },
  exportFunction: {
    type: Function,
    required: true
  },
  exportParams: {
    type: Object,
    default: () => ({})
  },
  confirmMessage: {
    type: String,
    default: '是否确认导出数据？'
  },
  successMessage: {
    type: String,
    default: '导出成功'
  },
  permissions: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['before-export', 'success', 'error'])

const { proxy } = getCurrentInstance()
const loading = ref(false)

// 处理导出
const handleExport = async () => {
  try {
    // 触发导出前事件
    emit('before-export', props.exportParams)
    
    // 确认对话框
    await proxy.$modal.confirm(props.confirmMessage)
    
    loading.value = true
    
    // 执行导出函数
    await props.exportFunction(props.exportParams)
    
    proxy.$modal.msgSuccess(props.successMessage)
    emit('success', props.exportParams)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出失败:', error)
      proxy.$modal.msgError('导出失败')
      emit('error', error)
    }
  } finally {
    loading.value = false
  }
}
</script>
