<template>
  <el-dialog title="CDK生成成功" v-model="visible" width="600px" append-to-body>
    <div class="cdk-success-content">
      <!-- 成功提示 -->
      <div class="success-header">
        <el-icon color="#67C23A" size="24">
          <SuccessFilled/>
        </el-icon>
        <span style="margin-left: 10px; font-size: 16px; font-weight: bold;">
          成功生成 {{ generatedCdks.length }} 个CDK
        </span>
      </div>

      <!-- CDK列表 -->
      <div class="cdk-list">
        <div
            v-for="(cdk, index) in generatedCdks"
            :key="index"
            class="cdk-item"
        >
          <span class="cdk-code">{{ cdk.cdk || cdk }}</span>
          <CopyButton 
              :copy-text="cdk.cdk || cdk"
              :success-message="`CDK码 ${cdk.cdk || cdk} 已复制到剪贴板`"
              size="small"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="operation-buttons">
        <el-button type="primary" @click="copyAllCdkCodes">
          <el-icon>
            <CopyDocument/>
          </el-icon>
          复制所有CDK码
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleContinue">继续生成</el-button>
        <el-button @click="visible = false">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from 'vue'
import { SuccessFilled, CopyDocument } from '@element-plus/icons-vue'
import CopyButton from './CopyButton.vue'
import ClipboardJS from 'clipboard'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  generatedCdks: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'continue'])

const { proxy } = getCurrentInstance()

// 响应式数据
const visible = ref(props.modelValue)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 复制所有CDK码
const copyAllCdkCodes = () => {
  const allCdks = props.generatedCdks.map(cdk => cdk.cdk || cdk).join('\n')
  try {
    const success = ClipboardJS.copy(allCdks)
    if (success) {
      proxy.$modal.msgSuccess(`已复制 ${props.generatedCdks.length} 个CDK码到剪贴板`)
    } else {
      throw new Error('复制失败')
    }
  } catch (error) {
    console.error('复制失败:', error)
    proxy.$modal.msgError('复制失败，请手动复制')
  }
}

// 继续生成
const handleContinue = () => {
  emit('continue')
}
</script>

<style scoped>
.cdk-success-content {
  padding: 10px 0;
}

.success-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #67C23A;
}

.cdk-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.cdk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.cdk-item:last-child {
  border-bottom: none;
}

.cdk-code {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 15px;
  background-color: #fafafa;
  border-radius: 8px;
}
</style>
