<template>
  <div class="user-exchange-records">
    <!-- 用户搜索区域 -->
    <el-card class="box-card" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户搜索</span>
        </div>
      </template>
      <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item label="选择用户" prop="userId">
          <UserSelector
              v-model="queryParams.userId"
              @change="handleUserChange"
          />
        </el-form-item>
        <el-form-item label="CDK类型" prop="cdkType">
          <el-select v-model="queryParams.cdkType" placeholder="请选择CDK类型" clearable style="width: 150px">
            <el-option
                v-for="dict in cdk_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="兑换时间" prop="exchangeTime">
          <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 240px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" :disabled="!queryParams.userId">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户信息显示 -->
    <el-card v-if="selectedUserInfo" class="box-card" style="margin-bottom: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户信息</span>
        </div>
      </template>
      <el-descriptions :column="4" border>
        <el-descriptions-item label="用户ID">{{ selectedUserInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="昵称">{{ selectedUserInfo.nickname }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ selectedUserInfo.phone }}</el-descriptions-item>
        <el-descriptions-item label="身份类型">{{ selectedUserInfo.identityName }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" v-if="queryParams.userId">
      <el-col :span="1.5">
        <ExportButton
            text="导出兑换记录"
            :export-function="exportUserCdkExchangeRecords"
            :export-params="getExportParams()"
            :permissions="['system:cdk:export']"
            :disabled="!queryParams.userId"
        />
      </el-col>
    </el-row>

    <!-- 兑换记录列表 -->
    <el-table v-loading="loading" :data="recordsList" v-if="queryParams.userId">
      <el-table-column label="CDK码" align="center" prop="cdkCode" min-width="180">
        <template #default="scope">
          <span>{{ scope.row.cdkCode }}</span>
          <CopyButton 
              :copy-text="scope.row.cdkCode"
              style="margin-left: 8px"
              size="small"
          />
        </template>
      </el-table-column>
      <el-table-column label="CDK类型" align="center" prop="cdkTypeName" width="100"/>
      <el-table-column label="CDK价值" align="center" prop="cdkValue" width="120"/>
      <el-table-column label="兑换时间" align="center" prop="exchangeTime" width="160"/>
      <el-table-column label="兑换来源" align="center" prop="exchangeSourceName" width="120"/>
      <el-table-column label="兑换状态" align="center" prop="exchangeStatus" width="100">
        <template #default="scope">
          <el-tag type="success">{{ scope.row.exchangeStatus }}</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- 空状态提示 -->
    <el-empty v-if="!queryParams.userId" description="请先选择用户查看兑换记录" />

    <!-- 分页 -->
    <pagination
        v-show="total > 0 && queryParams.userId"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getRecordsList"
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, reactive } from 'vue'
import { getUserCdkExchangeRecords, exportUserCdkExchangeRecords } from '@/api/cdk'
import UserSelector from './UserSelector.vue'
import CopyButton from './CopyButton.vue'
import ExportButton from './ExportButton.vue'

const { proxy } = getCurrentInstance()
const { cdk_type } = proxy.useDict("cdk_type")

// 响应式数据
const loading = ref(false)
const recordsList = ref([])
const total = ref(0)
const selectedUserInfo = ref(null)
const dateRange = ref([])

// 查询参数
const queryParams = reactive({
  userId: null,
  cdkType: null,
  startDate: null,
  endDate: null,
  pageNum: 1,
  pageSize: 10
})

// 处理用户选择变化
const handleUserChange = (userId, userInfo) => {
  if (userId && userInfo) {
    selectedUserInfo.value = userInfo
    // 自动查询该用户的兑换记录
    getRecordsList()
  } else {
    selectedUserInfo.value = null
    recordsList.value = []
    total.value = 0
  }
}

// 搜索按钮操作
const handleQuery = () => {
  if (!queryParams.userId) {
    proxy.$modal.msgWarning('请先选择用户')
    return
  }
  
  // 处理日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startDate = dateRange.value[0]
    queryParams.endDate = dateRange.value[1]
  } else {
    queryParams.startDate = null
    queryParams.endDate = null
  }
  
  queryParams.pageNum = 1
  getRecordsList()
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    userId: null,
    cdkType: null,
    startDate: null,
    endDate: null,
    pageNum: 1,
    pageSize: 10
  })
  dateRange.value = []
  selectedUserInfo.value = null
  recordsList.value = []
  total.value = 0
}

// 获取兑换记录列表
const getRecordsList = () => {
  if (!queryParams.userId) {
    return
  }
  
  loading.value = true
  
  const params = {
    userId: queryParams.userId,
    cdkType: queryParams.cdkType,
    startDate: queryParams.startDate,
    endDate: queryParams.endDate,
    pageNum: queryParams.pageNum,
    pageSize: queryParams.pageSize
  }
  
  getUserCdkExchangeRecords(params).then(response => {
    recordsList.value = response.rows || []
    total.value = response.total || 0
  }).catch(() => {
    recordsList.value = []
    total.value = 0
  }).finally(() => {
    loading.value = false
  })
}

// 获取导出参数
const getExportParams = () => {
  return {
    userId: queryParams.userId,
    cdkType: queryParams.cdkType,
    startDate: queryParams.startDate,
    endDate: queryParams.endDate
  }
}

// 暴露方法给父组件
defineExpose({
  resetQuery,
  getRecordsList
})
</script>

<style scoped>
.user-exchange-records {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
