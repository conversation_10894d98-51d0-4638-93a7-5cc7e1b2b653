<template>
  <el-button 
      :link="link"
      :type="type" 
      :icon="icon"
      :size="size"
      :style="style"
      @click="handleCopy"
  >
    <slot>{{ text }}</slot>
  </el-button>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import { CopyDocument } from '@element-plus/icons-vue'
import ClipboardJS from 'clipboard'

// Props
const props = defineProps({
  text: {
    type: String,
    default: '复制'
  },
  copyText: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: 'small'
  },
  icon: {
    type: [String, Object],
    default: () => CopyDocument
  },
  link: {
    type: Boolean,
    default: true
  },
  style: {
    type: [String, Object],
    default: ''
  },
  successMessage: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['success', 'error'])

const { proxy } = getCurrentInstance()

// 复制功能
const handleCopy = () => {
  try {
    // 使用clipboard.js的静态方法直接复制文本
    const success = ClipboardJS.copy(props.copyText)
    if (success) {
      const message = props.successMessage || `${props.copyText} 已复制到剪贴板`
      proxy.$modal.msgSuccess(message)
      emit('success', props.copyText)
    } else {
      throw new Error('复制失败')
    }
  } catch (error) {
    console.error('复制失败:', error)
    proxy.$modal.msgError('复制失败，请手动复制')
    emit('error', error)
  }
}
</script>
