<template>
  <div class="item-selector">
    <!-- 搜索框 -->
    <el-input
        v-model="searchKey"
        placeholder="搜索物品名称..."
        clearable
        style="margin-bottom: 15px;"
    >
      <template #prefix>
        <el-icon>
          <Search/>
        </el-icon>
      </template>
    </el-input>

    <!-- 物品列表 -->
    <div class="item-list" v-loading="loading" element-loading-text="正在加载物品列表...">
      <template v-if="!loading && filteredItemList.length > 0">
        <div
            v-for="item in filteredItemList"
            :key="item.id"
            class="item-card"
            :class="{ 'selected': selectedValue === item.id }"
            @click="selectItem(item)"
        >
          <div class="item-image">
            <image-preview
                v-if="item.image"
                :src="item.image"
                width="60"
                height="60"
            />
            <div v-else class="no-image">无图片</div>
          </div>
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-id">ID: {{ item.id }}</div>
          </div>
        </div>
      </template>
      <div v-else-if="!loading && filteredItemList.length === 0" class="no-items">
        {{ itemList.length === 0 ? '暂无物品数据' : '没有找到匹配的物品' }}
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { listCommoditys } from '@/api/commoditySys/commoditys'
import ImagePreview from '@/components/ImagePreview/index.vue'

// Props
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const selectedValue = ref(props.modelValue)
const searchKey = ref('')
const itemList = ref([])
const loading = ref(false)
const isInitialized = ref(false) // 防止重复初始化

// 计算属性
const filteredItemList = computed(() => {
  if (!searchKey.value) {
    return itemList.value
  }
  return itemList.value.filter(item =>
    item.name.toLowerCase().includes(searchKey.value.toLowerCase())
  )
})

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedValue.value = newVal
})

// 监听selectedValue变化
watch(selectedValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 选择物品
const selectItem = (item) => {
  selectedValue.value = item.id
}

// 获取所有物品
const getAllItems = async () => {
  // 防止重复请求
  if (loading.value || isInitialized.value) {
    console.log('ItemSelector: 请求已在进行中或已初始化，跳过重复请求')
    return
  }

  console.log('ItemSelector: 开始获取物品列表')
  loading.value = true

  try {
    // 设置一个较大的pageSize来获取所有物品
    const params = {
      pageNum: 1,
      pageSize: 10000 // 设置一个足够大的数值来获取所有物品
    }

    const response = await listCommoditys(params)
    itemList.value = response.rows || []
    isInitialized.value = true // 标记已初始化
    console.log('ItemSelector: 物品列表获取成功，共', itemList.value.length, '个物品')
  } catch (error) {
    console.error('ItemSelector: 获取物品列表失败', error)
    itemList.value = []
  } finally {
    loading.value = false
  }
}

// 手动刷新物品列表
const refreshItems = () => {
  isInitialized.value = false
  getAllItems()
}

// 页面加载时获取物品列表
onMounted(() => {
  console.log('ItemSelector: 组件已挂载，开始初始化')
  getAllItems()
})

// 暴露刷新方法给父组件
defineExpose({
  refreshItems
})
</script>

<style scoped>
.item-selector {
  width: 100%;
}

.item-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.item-card {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  width: calc(50% - 5px);
  min-width: 200px;
}

.item-card:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.item-card.selected {
  border-color: #409EFF;
  background-color: #e6f7ff;
}

.item-image {
  min-width: 60px;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.item-name {
  font-weight: bold;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 5px;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-id {
  font-size: 12px;
  color: #606266;
}

.no-items {
  width: 100%;
  padding: 20px;
  text-align: center;
  color: #909399;
}


</style>
