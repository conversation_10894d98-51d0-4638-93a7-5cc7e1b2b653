<template>
  <el-dialog title="批量生成CDK" v-model="visible" width="500px" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="生成数量" prop="count">
        <el-input-number v-model="form.count" :min="1" :max="1000"/>
      </el-form-item>
      
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择CDK类型" @change="handleTypeChange">
          <el-option
              v-for="dict in cdk_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="绑定用户" prop="foruser">
        <UserSelector
            v-model="form.foruser"
            placeholder="请选择绑定用户"
            @user-selected="handleUserSelected"
        />
        <div class="input-help-text">
          输入用户昵称或手机号进行搜索，必须选择一个用户
        </div>
      </el-form-item>

      <el-form-item label="CDK信息" prop="info">
        <el-radio-group v-model="form.info">
          <el-radio label="幸运id">幸运id</el-radio>
          <el-radio label="全补">全补</el-radio>
          <el-radio label="半补">半补</el-radio>
          <el-radio label="转发长智">转发长智</el-radio>
        </el-radio-group>
        <div class="input-help-text">
          请选择CDK的信息类型
        </div>
      </el-form-item>

      <!-- 用户详细信息展示 -->
      <el-form-item v-if="selectedUserInfo" label="用户信息">
        <div class="user-detail-info">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="用户昵称">{{ selectedUserInfo.nickname }}</el-descriptions-item>
            <el-descriptions-item label="用户等级">{{ selectedUserInfo.level }}</el-descriptions-item>
            <el-descriptions-item label="用户身份">{{ selectedUserInfo.identityName }}</el-descriptions-item>
            <el-descriptions-item label="上级用户">{{ selectedUserInfo.superiorDisplayInfo }}</el-descriptions-item>
            <el-descriptions-item label="钥匙数量">{{ selectedUserInfo.keyAmount }}</el-descriptions-item>
            <el-descriptions-item label="账户余额">{{ selectedUserInfo.balance }}</el-descriptions-item>
            <el-descriptions-item label="背包价值" span="2">{{ selectedUserInfo.backpackValue }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-form-item>
      
      <el-form-item v-if="form.type !== '3'" label="数量" prop="value">
        <el-input-number v-model="form.value" :min="1"/>
        <div class="input-help-text">
          {{ getValuePlaceholder() }}
        </div>
      </el-form-item>

      <el-form-item v-if="form.type === '3'" label="选择物品" prop="value">
        <ItemSelector v-model="form.value" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from 'vue'
import { batchGenerateCdk, getUserDetailInfo } from '@/api/cdk'
import { getValuePlaceholder } from '@/utils/cdkUtils'
import UserSelector from './UserSelector.vue'
import ItemSelector from './ItemSelector.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

const { proxy } = getCurrentInstance()
const { cdk_type } = proxy.useDict("cdk_type")

// 响应式数据
const visible = ref(props.modelValue)
const submitting = ref(false)
const formRef = ref()
const selectedUserInfo = ref(null)

// 表单数据
const form = ref({
  count: 10,
  type: undefined,
  value: 1,
  foruser: 0,
  info: '幸运id'
})

// 表单验证规则
const rules = {
  count: [{ required: true, message: "生成数量不能为空", trigger: "blur" }],
  type: [{ required: true, message: "CDK类型不能为空", trigger: "change" }],
  value: [{ required: true, message: "数量/物品ID不能为空", trigger: "change" }],
  info: [{ required: true, message: "CDK信息类型不能为空", trigger: "change" }]
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (newVal) {
    resetForm()
  }
})

// 处理类型变化
const handleTypeChange = () => {
  if (form.value.type !== '3') {
    form.value.value = 1
  }
}

// 处理用户选择
const handleUserSelected = async (userId, selectedUser) => {
  console.log('handleUserSelected called with:', userId, selectedUser)

  if (userId && userId > 0) {
    try {
      console.log('Calling getUserDetailInfo API with userId:', userId)
      const response = await getUserDetailInfo(userId)
      console.log('getUserDetailInfo response:', response)

      if (response.code === 200) {
        selectedUserInfo.value = response.data
        console.log('User detail info set:', selectedUserInfo.value)
      } else {
        proxy.$modal.msgError(response.msg || '获取用户信息失败')
        selectedUserInfo.value = null
      }
    } catch (error) {
      console.error('获取用户详细信息失败:', error)
      proxy.$modal.msgError('获取用户信息失败')
      selectedUserInfo.value = null
    }
  } else {
    console.log('No valid userId, clearing selectedUserInfo')
    selectedUserInfo.value = null
  }
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true
      batchGenerateCdk(form.value).then(response => {
        proxy.$modal.msgSuccess("批量生成CDK成功")
        emit('success', {
          type: 'batch',
          data: response.data
        })
      }).catch(() => {
        submitting.value = false
      }).finally(() => {
        submitting.value = false
      })
    }
  })
}

// 取消
const cancel = () => {
  visible.value = false
}

// 重置表单
const resetForm = () => {
  form.value = {
    count: 10,
    type: undefined,
    value: 1,
    foruser: 0,
    info: '幸运id'
  }
  selectedUserInfo.value = null
  formRef.value?.resetFields()
}
</script>

<style scoped>
.input-help-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}

.user-detail-info {
  margin-top: 10px;
}

.user-detail-info .el-descriptions {
  background-color: #f8f9fa;
  border-radius: 4px;
}
</style>
