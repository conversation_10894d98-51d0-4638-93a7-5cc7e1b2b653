<template>
  <div
    class="hamburger-wrapper"
    @click="handleClick"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
  >
    <svg
      :class="{'is-active':isActive}"
      class="hamburger"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="64"
      height="64"
    >
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z" />
    </svg>
  </div>
</template>

<script setup>
defineProps({
  isActive: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits()

// 防止触摸和点击事件重复触发
let touchStarted = false

const handleClick = (event) => {
  // 如果是触摸设备且已经处理了触摸事件，则忽略点击事件
  if (touchStarted) {
    touchStarted = false
    return
  }
  console.log('Hamburger clicked (mouse/keyboard)')
  emit('toggleClick')
}

const handleTouchStart = (event) => {
  touchStarted = true
  console.log('Hamburger touch start')
  // 防止默认的触摸行为（如滚动）
  event.preventDefault()
}

const handleTouchEnd = (event) => {
  console.log('Hamburger touch end')
  // 防止默认行为和事件冒泡
  event.preventDefault()
  event.stopPropagation()

  // 触发菜单切换
  emit('toggleClick')

  // 延迟重置标志，确保click事件被正确忽略
  setTimeout(() => {
    touchStarted = false
  }, 300)
}
</script>

<style scoped>
.hamburger-wrapper {
  /* 确保足够大的触摸区域 - 移动端最佳实践 */
  min-width: 44px;
  min-height: 44px;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 确保按钮可以接收触摸事件 */
  pointer-events: auto;
  position: relative;
  z-index: 1000;

  /* 过渡效果 */
  transition: background-color 0.2s ease;
}

.hamburger-wrapper:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.hamburger-wrapper:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.hamburger {
  display: block;
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.hamburger.is-active {
  transform: rotate(180deg);
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  .hamburger-wrapper {
    min-width: 48px;
    min-height: 48px;
    padding: 14px 16px;
  }

  .hamburger {
    width: 22px;
    height: 22px;
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hamburger-wrapper {
    /* 在高DPI屏幕上确保清晰的边界 */
    border-radius: 2px;
  }
}
</style>
