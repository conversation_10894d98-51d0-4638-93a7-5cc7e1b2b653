<template>
  <div class="icp-info" :class="{ 'dark-theme': isDarkTheme }">
    <div class="icp-item">
      <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">
        冀ICP备2024097556号-6
      </a>
    </div>
    <div class="icp-item">
      <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=13040302001687" target="_blank" rel="noopener noreferrer">
        <img src="@/assets/images/police-Deb_aa6E.png" alt="公安备案" class="police-logo" />
        冀公网安备13040302001687号
      </a>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import useSettingsStore from '@/store/modules/settings'

const props = defineProps({
  theme: {
    type: String,
    default: 'auto' // 'auto', 'light', 'dark'
  }
})

const settingsStore = useSettingsStore()

const isDarkTheme = computed(() => {
  if (props.theme === 'dark') return true
  if (props.theme === 'light') return false
  // auto模式下根据系统主题判断
  return settingsStore.sideTheme === 'theme-dark'
})
</script>

<style lang="scss" scoped>
.icp-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;

  .icp-item {
    display: flex;
    align-items: center;
    gap: 4px;

    a {
      color: inherit;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: color 0.3s ease;

      &:hover {
        color: #ccc;
      }
    }

    .police-logo {
      width: 14px;
      height: 14px;
      vertical-align: middle;
    }
  }

  // 深色主题样式
  &.dark-theme {
    color: #999;

    .icp-item a:hover {
      color: #666;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    font-size: 11px;
    gap: 2px;

    .icp-item {
      gap: 2px;

      .police-logo {
        width: 12px;
        height: 12px;
      }
    }
  }
}

// 水平排列样式（可选）
.icp-info.horizontal {
  flex-direction: row;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2px;
  }
}

// 内联样式（用于与其他文本在同一行）
.icp-info.inline {
  flex-direction: row;
  gap: 8px;
  display: inline-flex;

  @media (max-width: 768px) {
    flex-direction: row;
    gap: 4px;
  }
}
</style>
