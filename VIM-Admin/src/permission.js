import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login'];

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      const userStore = useUserStore()
      const permissionStore = usePermissionStore()

      if (userStore.roles.length === 0) {
        //  页面刷新时主动获取用户信息
        try {
          // 获取用户信息
          await userStore.getInfo()
          // 生成路由
          const accessRoutes = await permissionStore.generateRoutes(userStore.roles)
          // 动态添加可访问路由表
          accessRoutes.forEach(route => {
            router.addRoute(route)
          })

          // 检查用户身份，如果是主播且访问首页，则跳转到我的推广页面
          if (to.path === '/' || to.path === '/index') {
            // 检查用户是否有主播身份（通过手机号查询vim_user表的identity字段）
            try {
              const { checkUserIdentity } = await import('@/api/promotion/anchor')
              const identityResult = await checkUserIdentity()
              if (identityResult.data && (identityResult.data.identity === 2 || identityResult.data.identity === 3)) {
                // 用户是主播身份，跳转到我的推广页面
                next({ path: '/promotion/anchor/users', replace: true })
                return
              }
            } catch (error) {
              console.log('检查用户身份失败，继续正常流程:', error)
            }
          }

          // 确保addRoutes已完成，set the replace: true, so the navigation will not leave a history record
          next({ ...to, replace: true })
        } catch (error) {
          // 获取用户信息失败，可能token已过期
          console.error('获取用户信息失败:', error)
          await userStore.logOut()
          ElMessage.error('登录状态已过期，请重新登录')
          next(`/login?redirect=${to.fullPath}`)
          NProgress.done()
        }
      } else {
        // 用户信息已存在，检查主播身份跳转逻辑
        if (to.path === '/' || to.path === '/index') {
          try {
            const { checkUserIdentity } = await import('@/api/promotion/anchor')
            const identityResult = await checkUserIdentity()
            if (identityResult.data && (identityResult.data.identity === 2 || identityResult.data.identity === 3)) {
              // 用户是主播身份，跳转到我的推广页面
              next({ path: '/promotion/anchor/users', replace: true })
              return
            }
          } catch (error) {
            console.log('检查用户身份失败，继续正常流程:', error)
          }
        }
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
