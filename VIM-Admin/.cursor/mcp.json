{"mcpServers": {"browser-tools": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true}, "server-sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/server-sequential-thinking", "--key", "48eacb89-e170-46a4-b9a0-7ffa19f7a79c"]}, "server-filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Desktop/java/VIM_Server/VIM-Admin"]}}}