---
description: 
globs: 
alwaysApply: true
---
# 编码风格和最佳实践

本项目遵循以下编码风格和最佳实践指南：

## Vue 组件风格

- 使用 Vue 3 的 Composition API
- 使用 `<script setup>` 语法
- 使用 `ref`、`reactive`、`computed` 等 Composition API 特性
- 组件命名采用 PascalCase
- Props 定义明确类型

```vue
<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update', 'delete'])

const data = ref([])
const loading = ref(false)

// ...其他逻辑
</script>
```

## CSS 风格

- 使用 SCSS 预处理器
- 使用 scoped 样式
- 变量定义在 variables.scss 中
- 使用 BEM 命名约定 (Block__Element--Modifier)

```vue
<style lang="scss" scoped>
.my-component {
  &__header {
    // ...
  }
  
  &__content {
    // ...
  }
  
  &--active {
    // ...
  }
}
</style>
```

## JavaScript 风格

- 使用 ES6+ 语法
- 使用箭头函数
- 使用 async/await 处理异步
- 使用解构赋值
- 使用模板字符串
- 优先使用 const 和 let，避免 var

```js
// 异步处理
const fetchData = async () => {
  try {
    loading.value = true
    const { data: result } = await getList(query.value)
    data.value = result.list
    total.value = result.total
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 解构赋值
const { name, age } = user
```

## 文件组织

- 单一职责原则
- 相关功能放在同一目录
- API 请求封装在 api 目录
- 工具函数封装在 utils 目录
- 公共组件放在 components 目录
- 视图组件放在 views 目录

## 性能优化

- 使用路由懒加载
- 避免不必要的组件渲染
- 合理使用计算属性和缓存
- 避免大对象和大数组的响应式

## 其他最佳实践

- 添加适当的注释说明复杂逻辑
- 错误处理和日志记录
- 使用常量定义固定值
- 使用工具函数抽取重复逻辑

