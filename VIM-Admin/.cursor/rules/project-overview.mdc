---
description: 
globs: 
alwaysApply: true
---
# 项目概述

这个项目是基于若依(RuoYi)框架的Vue3前端管理系统，使用了以下主要技术和框架：

- **前端框架**: Vue 3 + Vite
- **UI框架**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **网络请求**: Axios
- **CSS预处理器**: SCSS

## 主要文件和目录

- [主入口文件](mdc:src/main.js) - Vue应用的主入口文件
- [应用组件](mdc:src/App.vue) - 根组件
- [路由配置](mdc:src/router/index.js) - 定义应用路由
- [权限控制](mdc:src/permission.js) - 处理路由权限和认证
- [布局组件](mdc:src/layout/index.vue) - 主布局模板
- [请求工具](mdc:src/utils/request.js) - Axios请求封装

## 项目结构

- `/src/api` - 后端API接口
- `/src/assets` - 静态资源
- `/src/components` - 公共组件
- `/src/layout` - 布局相关组件
- `/src/router` - 路由配置
- `/src/store` - Pinia状态管理
- `/src/utils` - 工具函数
- `/src/views` - 页面视图组件

## 重要概念

- 使用动态路由控制和权限管理
- 基于Token的认证机制
- 界面布局包含侧边栏、顶部导航和标签页
- 支持多主题和自定义设置


