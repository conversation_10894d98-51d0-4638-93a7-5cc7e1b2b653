---
description: 
globs: 
alwaysApply: true
---
# 组件系统

项目使用了模块化的组件系统，主要包括布局组件、功能组件和业务组件。

## 布局组件

布局组件定义在 [layout](mdc:src/layout) 目录中，主要包括：

- [主布局](mdc:src/layout/index.vue) - 应用的主要布局
- 侧边栏 (Sidebar) - 负责导航菜单显示
- 顶部导航栏 (Navbar) - 显示顶部操作按钮
- 标签视图 (TagsView) - 多标签页导航
- 应用主体 (AppMain) - 主要内容区域

## 公共组件

公共组件定义在 [components](mdc:src/components) 目录中，主要包括：

- 面包屑导航 (Breadcrumb)
- 文件上传 (FileUpload)
- 图片上传 (ImageUpload)
- SVG图标 (SvgIcon)
- 富文本编辑器 (Editor)
- 树选择 (TreeSelect)
- 分页组件 (Pagination)
- 等其他通用组件

## 业务组件

业务组件主要位于各个业务模块下的 views 目录中，如：

- 系统管理相关组件 - `/src/views/system`
- 监控相关组件 - `/src/views/monitor`
- 工具相关组件 - `/src/views/tool`
- 其他业务相关组件

## 组件注册

全局组件在 [main.js](mdc:src/main.js) 中注册，可以在任何页面直接使用：

```js
// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
// ...其他全局组件
```

## 组件通信

组件间通信主要通过以下方式：

- Pinia 状态管理
- Props 和 Events
- 依赖注入 (provide/inject)
- 事件总线 (需要时)

