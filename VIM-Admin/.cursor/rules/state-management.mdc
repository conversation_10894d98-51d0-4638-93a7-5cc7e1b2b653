---
description: 
globs: 
alwaysApply: true
---
# 状态管理

项目使用 Pinia 作为状态管理库，主要的状态模块定义在 [store/modules](mdc:src/store/modules) 目录下。

## 主要状态模块

- [App 状态](mdc:src/store/modules/app.js) - 管理应用整体状态，如侧边栏的打开/关闭、设备类型等
- [用户状态](mdc:src/store/modules/user.js) - 管理用户信息、token、权限等
- [权限状态](mdc:src/store/modules/permission.js) - 管理路由权限
- [设置状态](mdc:src/store/modules/settings.js) - 管理应用设置，如主题、布局等
- [标签视图状态](mdc:src/store/modules/tagsView.js) - 管理多标签导航

## 定义状态模块

使用 Pinia 的 defineStore 来定义状态模块：

```js
const useAppStore = defineStore(
  'app',
  {
    state: () => ({
      // 状态定义
    }),
    actions: {
      // 操作方法
    }
  })
```

## 使用状态

在组件中使用状态：

```js
// 引入状态模块
import useUserStore from '@/store/modules/user'

// 使用状态
const userStore = useUserStore()
const token = userStore.token
const roles = userStore.roles

// 调用操作方法
userStore.login(userInfo)
userStore.getInfo()
userStore.logOut()
```

## 状态持久化

部分状态通过 Cookie 或 localStorage 实现持久化：

- Token 存储在 Cookie/LocalStorage 中
- 应用设置存储在 LocalStorage 中
- 侧边栏状态存储在 Cookie 中

