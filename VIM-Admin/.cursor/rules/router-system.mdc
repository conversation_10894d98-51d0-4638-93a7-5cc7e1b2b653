---
description:
globs:
alwaysApply: false
---
# 路由系统

项目使用 Vue Router 4 进行路由管理，并实现了基于权限的动态路由控制。

## 路由配置

主要的路由配置文件：

- [路由定义](mdc:src/router/index.js) - 定义基础路由和动态路由
- [权限控制](mdc:src/permission.js) - 路由导航守卫处理权限

## 路由类型

路由分为三种类型：

1. **常量路由** - 所有用户都可以访问的路由，如登录、注册、首页等
2. **动态路由** - 根据用户权限动态加载的路由
3. **异步路由** - 按需懒加载的路由

## 路由配置项

路由配置项包含以下特殊属性：

```js
{
  hidden: true,                     // 是否在侧边栏隐藏
  alwaysShow: true,                 // 总是显示根路由
  redirect: noRedirect,             // 重定向设置
  name: 'routerName',               // 路由名称
  query: '{"id": 1, "name": "ry"}', // 默认传递参数
  roles: ['admin', 'common'],       // 角色权限
  permissions: ['a:a:a', 'b:b:b'],  // 菜单权限
  meta: {
    noCache: true,                  // 是否缓存
    title: 'title',                 // 显示名称
    icon: 'svg-name',               // 图标
    breadcrumb: false,              // 面包屑中显示
    activeMenu: '/system/user'      // 高亮菜单
  }
}
```

## 路由权限控制

路由权限控制主要通过以下步骤实现：

1. 用户登录后获取权限信息
2. 根据权限过滤动态路由
3. 动态添加可访问路由到路由表
4. 无权限的路由重定向到401页面

## 路由懒加载

使用 Vue 的动态导入实现路由组件的懒加载：

```js
component: () => import('@/views/system/user/index')
```

## 路由守卫

在 [permission.js](mdc:src/permission.js) 中使用路由守卫处理权限控制和路由跳转：

- 全局前置守卫 `router.beforeEach` 处理权限验证
- 全局后置钩子 `router.afterEach` 处理进度条等
