---
description: 
globs: 
alwaysApply: true
---
# 认证与授权系统

这个项目使用基于Token的认证和基于角色的授权系统，主要通过以下文件和组件实现：

## 认证流程

- [权限控制](mdc:src/permission.js) - 路由导航守卫实现权限控制
- [用户状态](mdc:src/store/modules/user.js) - 用户状态管理，包括登录、获取用户信息和登出
- [认证工具](mdc:src/utils/auth.js) - Token处理工具
- [请求拦截](mdc:src/utils/request.js) - Axios请求拦截器自动添加Token

## 登录流程

1. 用户输入凭据并提交
2. 调用登录API获取Token
3. 存储Token到Cookie/LocalStorage
4. 获取用户信息和权限
5. 根据用户权限动态生成可访问路由

## 权限控制

- **路由权限控制**: 基于用户角色和权限动态生成可访问路由
- **按钮权限控制**: 使用自定义指令控制按钮显示
- **API权限控制**: 请求携带Token，后端验证权限

## 登出流程

1. 调用登出API
2. 清除本地Token
3. 重置用户状态
4. 重定向到登录页面

## 相关API

权限相关的API定义在API模块，包括登录、获取用户信息、登出等操作。

