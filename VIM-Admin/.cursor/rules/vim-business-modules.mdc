---
description: 
globs: 
alwaysApply: true
---
# VIM业务模块系统

该项目包含多个以"Vim"为前缀的业务模块，主要处理相关的业务逻辑。

## 主要业务模块

项目中的主要VIM业务模块包括：

- **VimBlackSys** - 黑名单系统
- **VimBlackItemSys** - 黑名单项目系统
- **vimBoxSys** - 盒子系统
- **VimBoxItemSys** - 盒子项目系统
- **vimBoxTypeSys** - 盒子类型系统
- **VimConditions** - 条件系统
- **VimOrderBoxSys** - 订单盒子系统
- **VimOrderClaimSys** - 订单认领系统
- **VimOrderRechargeSys** - 订单充值系统
- **VimPrizes** - 奖品系统
- **VimRollParticipants** - 抽奖参与者系统
- **VimRollWinners** - 抽奖获奖者系统
- **VimServerSeedSys** - 服务器种子系统
- **vimUpgradeSys** - 升级系统
- **VimUserSys** - 用户系统

## 模块结构

每个业务模块通常包含以下结构：

- **API层** - 位于 `src/api/模块名称` 目录，处理与后端的通信
- **视图层** - 位于 `src/views/模块名称` 目录，包含页面组件
- **组件层** - 一些模块可能在 `src/components` 目录下有特定组件

## 主要功能

这些模块主要实现以下功能：

- 数据的增删改查
- 业务流程的处理
- 数据的展示和交互
- 用户操作的响应

## 开发规范

在开发这些业务模块时，需要遵循以下规范：

1. API路径应与后端接口保持一致
2. 视图组件应使用统一的布局和风格
3. 共用逻辑应提取到公共组件或工具函数
4. 状态管理应明确职责划分

## 示例模块：vimUpgradeSys

以 vimUpgradeSys (突变物品管理) 为例，典型的业务模块包含：

- **API定义** - `src/api/vimUpgradeSys`
- **视图组件** - `src/views/vimUpgradeSys/upgrade`
- **路由配置** - 在路由文件中定义对应路由

```js
// 路由示例
{
  path: '/vimUpgradeSys',
  component: Layout,
  permissions: ['vimUpgradeSys:upgrade:list'],
  children: [
    {
      path: 'upgrade',
      component: () => import('@/views/vimUpgradeSys/upgrade/index'),
      name: 'VimUpgrade',
      meta: { title: '突变物品管理', icon: 'tool' }
    }
  ]
}
```

这些模块共同构成了系统的业务功能，通过组合和交互完成复杂的业务流程。

