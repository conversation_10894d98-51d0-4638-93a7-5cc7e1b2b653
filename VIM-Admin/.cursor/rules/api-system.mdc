---
description: 
globs: 
alwaysApply: true
---
# API请求系统

项目使用 Axios 进行API请求管理，并进行了统一的封装和处理。

## 请求封装

主要的请求封装文件：

- [请求工具](mdc:src/utils/request.js) - Axios的实例创建和拦截器配置

## 主要特性

- 统一的请求基础路径配置
- 统一的请求超时设置
- 请求拦截器自动添加Token
- 响应拦截器处理错误和状态码
- 请求防重复提交处理
- 统一的下载文件处理

## 请求拦截器

请求拦截器主要处理以下内容：

- 添加Token到请求头
- 处理GET请求参数
- 防止重复提交
- 数据大小限制检查

```js
// request拦截器
service.interceptors.request.use(config => {
  // 添加Token
  if (getToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  // 处理GET请求参数
  if (config.method === 'get' && config.params) {
    // ...处理参数
  }
  // 防止重复提交
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    // ...防重复提交逻辑
  }
  return config
})
```

## 响应拦截器

响应拦截器主要处理以下内容：

- 处理二进制数据响应
- 处理登录过期(401)
- 处理服务器错误(500)
- 处理业务错误
- 处理网络错误

```js
// 响应拦截器
service.interceptors.response.use(
  response => {
    // 处理二进制数据
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data
    }
    
    // 处理状态码
    const code = res.data.code || 200
    if (code === 401) {
      // 处理登录过期
    } else if (code === 500) {
      // 处理服务器错误
    } else if (code !== 200) {
      // 处理业务错误
    } else {
      return Promise.resolve(res.data)
    }
  },
  error => {
    // 处理网络错误
    console.log('err' + error)
    let { message } = error
    // ...错误消息处理
    ElMessage({ message: message, type: 'error', duration: 5 * 1000 })
    return Promise.reject(error)
  }
)
```

## 使用方式

在API模块中定义具体的请求方法，然后在组件中调用：

```js
// API定义
import request from '@/utils/request'

export function getList(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 组件中使用
import { getList } from '@/api/system/user'

getList(this.queryParams).then(response => {
  this.userList = response.rows
  this.total = response.total
})
```

## 文件下载

项目提供了统一的文件下载方法：

```js
import { download } from '@/utils/request'

download(url, params, filename)
```

